class PremiumPitch < ApplicationRecord
  include AASM
  has_paper_trail

  belongs_to :user
  after_commit :sync_mixpanel_user

  enum status: {
    to_be_pitched: 'to_be_pitched',
    wait_list: 'wait_list',
    interested: 'interested',
    rm_draft: 'rm_draft',
    badge_setup: 'badge_setup', # aka, rm_submitted
    badge_setup_no_layout_setup: 'badge_setup_no_layout_setup', # aka, rm_submitted
    layout_setup: 'layout_setup', # aka, boe_submitted
    pending_layout_approval: 'pending_layout_approval', # aka, oe_submitted
    trail_enabled: 'trail_enabled', # a.k.a layout approved & completed
    milestone_1: 'milestone_1',
    milestone_2: 'milestone_2',
    paid: 'paid',
    subscribed_no_layout: 'subscribed_no_layout',
    trial_low_usage: 'trial_low_usage',
  }

  enum lead_type: { LT_Inbound: 'LT_Inbound',
                    LT_Outbound: 'LT_Outbound',
                    RLT_Outbound: 'RLT_Outbound',
                    BLT_Inbound: 'BLT_Inbound',
                    BLT_Outbound: 'BLT_Outbound',
                    T_Outbound: 'T_Outbound',
                    OABLT_Outbound: 'OABLT_Outbound',
                    L_Inbound: 'L_Inbound',
                    BL_Inbound: 'BL_Inbound',
                    RLT_Inbound: 'RLT_Inbound',
                    LT_Referral: 'LT_Referral',
                    BLT_Referral: 'BLT_Referral',
                    LST_Inbound: 'LST_Inbound',
                    BLST_Inbound: 'BLST_Inbound',
  }

  enum source: { LEADER_PROFESSION: 'LEADER_PROFESSION',
                 LEADER_POSTER_SHARE: 'LEADER_POSTER_SHARE',
                 SELF_TRIAL: 'SELF_TRIAL',
                 WAITLIST: 'WAITLIST',
                 UNKNOWN: 'UNKNOWN',
                 PREMIUM_PITCH_INTERESTED: 'PREMIUM_PITCH_INTERESTED',
                 WATI_CAMPAIGN: 'WATI_CAMPAIGN',
                 WATI_NETWORK_DENSITY: 'WATI_NETWORK_DENSITY',
                 WATI_LEADER_CONTACTS: 'WATI_LEADER_CONTACTS',
                 REFERRAL_TO_RM: 'REFERRAL_TO_RM',
                 HIGH_END_DEVICE_NON_LEADER: 'HIGH_END_DEVICE_NON_LEADER',
                 MANUAL: 'MANUAL',
                 MANUAL_HIGH_END_DEVICE: 'MANUAL_HIGH_END_DEVICE',
                 HIGH_END_DEVICE: 'HIGH_END_DEVICE',
                 NO_LAYOUT_CUST_SUPPORT: 'NO_LAYOUT_CUST_SUPPORT',
                 APP_INSTALL_ML_PB: 'APP_INSTALL_ML_PB',
                 APP_UNINSTALL_ML_PB: 'APP_UNINSTALL_ML_PB',
                 APP_INSTALL_POLITICAL_SUPER_SET: 'APP_INSTALL_POLITICAL_SUPER_SET',
                 APP_UNINSTALL_POLITICAL_SUPER_SET: 'APP_UNINSTALL_POLITICAL_SUPER_SET',
  }

  YEARLY_PITCH_SOURCES = %w[LEADER_PROFESSION LEADER_POSTER_SHARE REFERRAL_TO_RM]

  attribute :lead_type, :string
  # Note: [Unknown] Had to set lead_type as attribute even though it is already defined as enum
  # https://stackoverflow.com/a/77830535/2256696

  scope :eligible_for_qualified_pitch, -> { where(status: [:trial_enabled, :milestone_1]) }

  aasm column: :status, enum: true do
    state :to_be_pitched, initial: true
    state :wait_list
    state :interested

    state :rm_draft
    state :badge_setup
    state :badge_setup_no_layout_setup
    state :layout_setup
    state :pending_layout_approval

    state :trail_enabled
    state :milestone_1
    state :milestone_2
    state :paid
    state :subscribed_no_layout
    state :trial_low_usage

    before_all_events :before_all_events

    event :mark_wait_list do
      transitions from: :to_be_pitched, to: :wait_list
    end
    event :shown_interest do
      transitions from: [:wait_list, :to_be_pitched], to: :interested, after: ->(new_tag = "", auto_assign = true) { after_shown_interest(new_tag, auto_assign) }
    end
    event :mark_as_subscribed_no_layout do
      transitions from: [:wait_list, :to_be_pitched, :interested], to: :subscribed_no_layout, after: :after_subscribed_no_layout
    end
    event :send_for_layout_approval do
      transitions from: [:rm_draft, :badge_setup_no_layout_setup, :layout_setup], to: :pending_layout_approval, after: :after_sending_for_layout_approval
    end

    event :layout_rejected do
      transitions from: :pending_layout_approval, to: :rm_draft, after: :after_layout_rejected
    end

    event :enabled_trial do
      transitions from: [:wait_list, :to_be_pitched, :interested, :pending_layout_approval], to: :trail_enabled, after: :after_trial_enabled
    end
    event :reached_milestone_1 do
      transitions from: :trail_enabled, to: :milestone_1, after: :after_milestone_1
    end
    event :reached_milestone_2 do
      transitions from: :milestone_1, to: :milestone_2, after: :after_milestone_2
    end
    event :mark_trial_low_usage do
      transitions from: [:trail_enabled, :milestone_1], to: :trial_low_usage, after: :after_trial_low_usage
    end

    # Jathara Events
    event :rm_drafted do
      transitions from: [:interested, :subscribed_no_layout, :pending_layout_approval], to: :rm_draft
    end
    event :setup_badge do
      transitions from: [:interested, :subscribed_no_layout, :rm_draft, :pending_layout_approval], to: :badge_setup, after: :after_badge_setup
    end
    event :setup_badge_no_layout_setup do
      transitions from: [:interested, :subscribed_no_layout, :rm_draft, :pending_layout_approval], to: :badge_setup_no_layout_setup, after: :after_badge_setup
    end
    event :setup_layout do
      transitions from: [:interested, :subscribed_no_layout, :badge_setup, :rm_draft, :pending_layout_approval], to: :layout_setup, after: :after_layout_setup
    end
    event :badge_incomplete do
      transitions from: [:badge_setup, :badge_setup_no_layout_setup], to: :rm_draft, after: :after_badge_incomplete
    end
    event :layout_incomplete do
      transitions from: :layout_setup, to: :rm_draft, after: :after_layout_incomplete
    end

    event :payment_done do
      transitions to: :paid, after: :after_paid
    end
  end
  DAILY_LEADS_COUNT_KEY_PREFIX = 'premium_pitch:daily_leads_count:'

  def self.todays_leads_count
    date_key = Time.zone.now.strftime('%Y-%m-%d')
    $redis.get("#{DAILY_LEADS_COUNT_KEY_PREFIX}#{date_key}").to_i
  end

  def is_pending_oe_review?
    badge_setup? || badge_setup_no_layout_setup? || layout_setup?
  end

  def is_layout_approval_flow_enabled?
    !is_self_trial_lead?
  end

  def is_self_trial_lead?
    SELF_TRIAL? || LST_Inbound? || BLST_Inbound?
  end

  def saved_layout
    if is_self_trial_lead?
      payment_done!
    elsif is_layout_approval_flow_enabled? && may_send_for_layout_approval?
      send_for_layout_approval!
    elsif may_enabled_trial?
      enabled_trial!
    end
  end

  def saved_badge
    if may_setup_layout?
      setup_layout!
    elsif is_self_trial_lead?
      payment_done!
    elsif is_layout_approval_flow_enabled? && may_send_for_layout_approval?
      send_for_layout_approval!
    end
  end

  private

  def increment_daily_leads_counter
    date_as_string = Time.zone.now.strftime('%Y-%m-%d')
    key = "#{DAILY_LEADS_COUNT_KEY_PREFIX}#{date_as_string}"
    $redis.incr(key)
    $redis.expireat(key, 2.days.from_now.end_of_day.to_i)
  end

  def after_shown_interest(new_tag = "", auto_assign = true)
    increment_daily_leads_counter
    Floww::AddLead.perform_async(user_id, lead_type, new_tag, auto_assign)
  end

  def after_sending_for_layout_approval
    upl = user.get_user_poster_layout_including_inactive
    if upl.present?
      upl.review_status = :awaited
      upl.save!
    end

    # Mark OE review as completed
    oe_status_metadata = UserMetadatum.find_or_initialize_by(user: user, key: Constants.oe_review_completed)
    oe_status_metadata.value = true
    oe_status_metadata.save!

    Floww::PendingLayoutApproval.perform_async(user_id)
  end

  def after_trial_enabled
    if is_layout_approval_flow_enabled?
      Floww::LayoutApproved.perform_async(user_id)
    else
      Floww::TrialEnabled.perform_async(user_id)
    end
  end

  def after_milestone_1
    Floww::TrialUsed.perform_async(user_id)
  end

  def after_milestone_2
    Floww::QualifiedPitch.perform_async(user_id)
  end

  def after_trial_low_usage
    Floww::TrialLowUsage.perform_async(user_id)
  end

  def after_paid
    Floww::Payment.perform_in(1.minutes, user_id)
  end

  def after_subscribed_no_layout
    increment_daily_leads_counter
    Floww::SubscribedNoLayout.perform_in(1.minutes, user_id, lead_type)
  end

  def sync_mixpanel_user
    SyncMixpanelUser.perform_async(user_id) if saved_change_to_crm_stage? || saved_change_to_source?
  end

  def after_layout_rejected
    if is_layout_approval_flow_enabled?
      Floww::LayoutRejected.perform_async(user_id)
    end
  end

  def after_badge_setup
    # Notify Floww about the badge_setup transition
    Floww::BadgeSetup.perform_async(user_id)
  end

  def after_layout_setup
    # Notify Floww about the layout_setup transition
    Floww::LayoutSetup.perform_async(user_id)

    # Assign OE after layout setup
    Floww::AssignOe.perform_async(user_id)
  end

  def after_badge_incomplete
    Floww::IncompleteLayoutBadge.perform_async(user_id)
  end

  def after_layout_incomplete
    Floww::IncompleteLayoutBadge.perform_async(user_id)
  end

  def before_all_events
    self.paper_trail_event = aasm.current_event
  end
end
